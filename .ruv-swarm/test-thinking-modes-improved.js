// Improved version demonstrating dev-full-thinking analysis

// Configuration constants for maintainability
const SCORING_CONFIG = {
    POINTS_PER_POST: 10,
    POINTS_PER_COMMENT: 5,
    KARMA_THRESHOLD: 1000,
    KARMA_MULTIPLIER: 1.5,
    EXPERT_THRESHOLD: 10000,
    DEFAULT_BONUS: 0
};

const USER_LEVELS = {
    EXPERT: 'Expert',
    ADVANCED: 'Advanced',
    INTERMEDIATE: 'Intermediate',
    BEGINNER: 'Beginner'
};

/**
 * Validates user object has required properties
 * @param {Object} user - User object to validate
 * @throws {Error} If user object is invalid
 */
function validateUser(user) {
    if (!user || typeof user !== 'object') {
        throw new Error('Invalid user object');
    }
    
    const requiredFields = ['posts', 'comments', 'karma'];
    const missingFields = requiredFields.filter(field => !(field in user));
    
    if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }
    
    // Validate numeric fields
    requiredFields.forEach(field => {
        if (typeof user[field] !== 'number' || user[field] < 0) {
            throw new Error(`${field} must be a non-negative number`);
        }
    });
}

/**
 * Safely parses user metadata
 * @param {string} metadata - JSON string containing metadata
 * @returns {Object} Parsed metadata or default object
 */
function parseUserMetadata(metadata) {
    if (!metadata) {
        return { bonus: SCORING_CONFIG.DEFAULT_BONUS };
    }
    
    try {
        const parsed = JSON.parse(metadata);
        return {
            bonus: typeof parsed.bonus === 'number' ? parsed.bonus : SCORING_CONFIG.DEFAULT_BONUS
        };
    } catch (error) {
        console.warn('Failed to parse user metadata:', error.message);
        return { bonus: SCORING_CONFIG.DEFAULT_BONUS };
    }
}

/**
 * Calculates base score from user activity
 * @param {Object} user - User object with posts and comments
 * @returns {number} Base score
 */
function calculateBaseScore(user) {
    const postScore = user.posts * SCORING_CONFIG.POINTS_PER_POST;
    const commentScore = user.comments * SCORING_CONFIG.POINTS_PER_COMMENT;
    return postScore + commentScore;
}

/**
 * Applies karma multiplier to score
 * @param {number} score - Base score
 * @param {number} karma - User karma
 * @returns {number} Modified score
 */
function applyKarmaBonus(score, karma) {
    if (karma > SCORING_CONFIG.KARMA_THRESHOLD) {
        return score * SCORING_CONFIG.KARMA_MULTIPLIER;
    }
    return score;
}

/**
 * Determines user level based on score
 * @param {number} score - User's total score
 * @returns {string} User level
 */
function getUserLevel(score) {
    if (score >= SCORING_CONFIG.EXPERT_THRESHOLD) {
        return USER_LEVELS.EXPERT;
    } else if (score >= 5000) {
        return USER_LEVELS.ADVANCED;
    } else if (score >= 1000) {
        return USER_LEVELS.INTERMEDIATE;
    }
    return USER_LEVELS.BEGINNER;
}

/**
 * Calculates user score and level
 * @param {Object} user - User object containing posts, comments, karma, and metadata
 * @returns {Object} Object containing score and level
 * @throws {Error} If user object is invalid
 */
function calculateUserScore(user) {
    // Input validation
    validateUser(user);
    
    // Calculate base score
    let score = calculateBaseScore(user);
    
    // Apply karma bonus
    score = applyKarmaBonus(score, user.karma);
    
    // Add bonus points from metadata
    const metadata = parseUserMetadata(user.metadata);
    score += metadata.bonus;
    
    // Determine user level
    const level = getUserLevel(score);
    
    return {
        score: Math.round(score),
        level,
        breakdown: {
            baseScore: calculateBaseScore(user),
            karmaMultiplier: user.karma > SCORING_CONFIG.KARMA_THRESHOLD ? SCORING_CONFIG.KARMA_MULTIPLIER : 1,
            bonusPoints: metadata.bonus
        }
    };
}

// Test suite
function runTests() {
    console.log('Running tests with thinking modes active...\n');
    
    // Test 1: Valid user
    const validUser = {
        posts: 50,
        comments: 100,
        karma: 2000,
        metadata: '{"bonus": 250}'
    };
    
    try {
        const result = calculateUserScore(validUser);
        console.log('✓ Valid user test passed:', result);
    } catch (error) {
        console.error('✗ Valid user test failed:', error.message);
    }
    
    // Test 2: Invalid metadata
    const userWithBadMetadata = {
        posts: 10,
        comments: 20,
        karma: 500,
        metadata: 'invalid json'
    };
    
    try {
        const result = calculateUserScore(userWithBadMetadata);
        console.log('✓ Invalid metadata test passed:', result);
    } catch (error) {
        console.error('✗ Invalid metadata test failed:', error.message);
    }
    
    // Test 3: Missing fields
    const incompleteUser = {
        posts: 10,
        comments: 20
        // missing karma
    };
    
    try {
        const result = calculateUserScore(incompleteUser);
        console.log('✗ Missing fields test failed - should have thrown error');
    } catch (error) {
        console.log('✓ Missing fields test passed:', error.message);
    }
    
    // Test 4: Edge cases
    const edgeCaseUser = {
        posts: 0,
        comments: 0,
        karma: 0,
        metadata: null
    };
    
    try {
        const result = calculateUserScore(edgeCaseUser);
        console.log('✓ Edge case test passed:', result);
    } catch (error) {
        console.error('✗ Edge case test failed:', error.message);
    }
}

// Run the tests
runTests();