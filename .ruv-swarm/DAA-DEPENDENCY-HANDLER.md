# DAA Workflow Dependency Handler

## 🚨 The Dependency Error You Encountered

**Error**: "Dependency analyze-core not completed for step evaluate-async"

This error occurs when you try to execute workflow steps in parallel that have dependencies on other steps.

## ✅ Solution: Smart Dependency Detection

### Dependency-Aware Workflow Execution

```javascript
// BEFORE executing any workflow, check for dependencies:

async function executeWorkflowSmart(workflowId, agentIds) {
  // 1. Get workflow details
  const workflow = getWorkflowById(workflowId);
  
  // 2. Check if workflow has dependencies
  const hasDependencies = workflow.dependencies && 
                         Object.keys(workflow.dependencies).length > 0;
  
  // 3. Execute based on dependency status
  if (hasDependencies) {
    console.log("⚠️ Workflow has dependencies - executing sequentially");
    
    // Execute steps in dependency order
    const executionOrder = topologicalSort(workflow.steps, workflow.dependencies);
    
    for (const stepId of executionOrder) {
      await mcp__ruv-swarm__daa_workflow_execute({
        workflow_id: workflowId,
        agentIds: agentIds,
        parallelExecution: false,  // CRITICAL: Must be false!
        stepFilter: stepId  // Execute one step at a time
      });
    }
  } else {
    console.log("✅ No dependencies - executing in parallel");
    
    await mcp__ruv-swarm__daa_workflow_execute({
      workflow_id: workflowId,
      agentIds: agentIds,
      parallelExecution: true  // Safe to parallelize
    });
  }
}
```

## 📋 Workflow Dependency Format

### Defining Dependencies in Workflows

```javascript
mcp__ruv-swarm__daa_workflow_create({
  id: "complex-workflow",
  name: "Complex Workflow with Dependencies",
  steps: [
    {
      id: "analyze-core",
      task: {
        method: "make_decision",
        args: [JSON.stringify({ context: "analyze core system" })]
      }
    },
    {
      id: "evaluate-async",
      task: {
        method: "make_decision",
        args: [JSON.stringify({ context: "evaluate async patterns" })]
      }
    }
  ],
  dependencies: {
    "evaluate-async": ["analyze-core"]  // evaluate-async depends on analyze-core
  },
  strategy: "sequential"  // Hint for dependency handling
})
```

## 🔄 Dependency Resolution Patterns

### 1. Linear Dependencies
```javascript
dependencies: {
  "step2": ["step1"],
  "step3": ["step2"],
  "step4": ["step3"]
}
// Execution order: step1 → step2 → step3 → step4
```

### 2. Parallel Groups with Dependencies
```javascript
dependencies: {
  "design-api": ["analyze-requirements"],
  "design-db": ["analyze-requirements"],
  "implement-api": ["design-api"],
  "implement-db": ["design-db"],
  "integrate": ["implement-api", "implement-db"]
}
// Can parallelize: design-api & design-db (after analyze-requirements)
// Can parallelize: implement-api & implement-db (after respective designs)
```

### 3. No Dependencies (Full Parallel)
```javascript
dependencies: {}  // or omit entirely
// All steps can execute in parallel
```

## 🛠️ Practical Implementation

### Complete Working Example

```javascript
// 1. Create workflow with dependencies
mcp__ruv-swarm__daa_workflow_create({
  id: "ms-framework-analysis",
  name: "Mister Smith Framework Analysis",
  steps: [
    {
      id: "load-context",
      task: {
        method: "make_decision",
        args: [JSON.stringify({ 
          context: "Load MS framework documentation",
          action: "initialize"
        })]
      }
    },
    {
      id: "analyze-core",
      task: {
        method: "make_decision",
        args: [JSON.stringify({ 
          context: "Analyze core architecture",
          requires: "framework context"
        })]
      }
    },
    {
      id: "evaluate-patterns",
      task: {
        method: "make_decision",
        args: [JSON.stringify({ 
          context: "Evaluate design patterns",
          requires: "core analysis"
        })]
      }
    }
  ],
  dependencies: {
    "analyze-core": ["load-context"],
    "evaluate-patterns": ["analyze-core"]
  },
  strategy: "sequential"
})

// 2. Execute with dependency awareness
mcp__ruv-swarm__daa_workflow_execute({
  workflow_id: "ms-framework-analysis",
  agentIds: ["daa-architect-001", "daa-analyst-001"],
  parallelExecution: false  // MUST be false due to dependencies!
})
```

## 🚦 Quick Decision Tree

```
Does workflow have dependencies?
├── NO → parallelExecution: true ✅
└── YES → parallelExecution: false ⚠️
    └── Consider breaking into parallel groups
```

## 📊 Monitoring Dependent Workflows

```javascript
// Check workflow execution status
mcp__ruv-swarm__task_status({ 
  taskId: "workflow-execution-id",
  detailed: true 
})

// Will show:
{
  "steps_completed": ["load-context", "analyze-core"],
  "steps_pending": ["evaluate-patterns"],
  "blocked_steps": [],
  "dependency_status": {
    "evaluate-patterns": {
      "waiting_for": [],
      "ready": true
    }
  }
}
```

## ⚡ Performance Tips

1. **Minimize Dependencies**: Only add necessary dependencies
2. **Group Parallel Work**: Identify steps that can run simultaneously
3. **Use Sub-Workflows**: Break large workflows into smaller parallel chunks
4. **Monitor Progress**: Use task_status to track dependency resolution

## 🎯 Key Takeaways

1. **Always check for dependencies before parallel execution**
2. **Use parallelExecution: false when dependencies exist**
3. **Define dependencies clearly in workflow creation**
4. **Monitor workflow progress to catch dependency issues**
5. **Consider workflow design to maximize parallelization**

---
**Remember**: The error "Dependency X not completed for step Y" means you tried to run steps in parallel that have a sequential dependency. Always check workflow.dependencies before setting parallelExecution: true.