# Claude-Flow Comprehensive Usage Guide
## For Workspace: `/Users/<USER>/Mister-<PERSON>/Mister-<PERSON>/`

---

## 🎯 **QUICK STATUS CHECK**

Your current installation:
- ✅ **Claude-Flow v1.0.72** (Latest)
- ✅ **17 SPARC Modes** Available
- ✅ **Configuration** Properly set up
- ⚠️ **System Status**: Partially Running (needs full startup)
- ❌ **MCP Server**: Stopped
- ❌ **Terminal Pool**: Stopped

---

## 🚀 **SYSTEM STARTUP (Essential First Steps)**

### **1. Start Full System**
```bash
cd /Users/<USER>/Mister-<PERSON>/<PERSON>-<PERSON>
./claude-flow start --ui --port 3000
```

### **2. Verify Everything is Running**
```bash
./claude-flow status
```
**Expected Output:**
```
✅ Claude-Flow System Status:
🟢 Status: Fully Running
🖥️  Terminal Pool: Active
🌐 MCP Server: Running
```

### **3. Access Web UI**
- Open: http://localhost:3000
- Monitor agents, tasks, and system health

---

## 🎯 **MULTI-AGENT PARALLEL BATCH WORKFLOWS**

### **🔥 PRIMARY TOOL: SWARM COORDINATION**

**For Complex Multi-Agent Projects:**
```bash
# Full-Stack Development (8 agents in parallel)
./claude-flow swarm "Build complete e-commerce platform with auth, API, database, frontend, tests, and deployment" \
  --strategy development \
  --mode hierarchical \
  --max-agents 8 \
  --parallel \
  --monitor \
  --review

# Research & Analysis (6 agents distributed)
./claude-flow swarm "Research and analyze modern web frameworks, security practices, and performance optimization" \
  --strategy research \
  --mode distributed \
  --max-agents 6 \
  --parallel \
  --output json

# System Optimization (hybrid coordination)
./claude-flow swarm "Optimize database performance, API response times, and frontend loading" \
  --strategy optimization \
  --mode hybrid \
  --max-agents 5 \
  --parallel \
  --monitor
```

### **⚡ SPARC BATCH EXECUTION**

**Using batch-executor Mode:**
```bash
# Parallel code generation
./claude-flow sparc run batch-executor "Generate user auth, API endpoints, database schema, and frontend components" --parallel

# Parallel analysis tasks
./claude-flow sparc run batch-executor "Analyze performance, security, code quality, and dependencies" --parallel --batch
```

### **🔧 SHELL-BASED PARALLEL EXECUTION**

**For Simple Parallel SPARC Tasks:**
```bash
# Run multiple SPARC modes in parallel
./claude-flow sparc run coder "user authentication system" --non-interactive &
./claude-flow sparc run coder "database schema design" --non-interactive &
./claude-flow sparc run coder "API endpoint implementation" --non-interactive &
./claude-flow sparc run coder "frontend components" --non-interactive &
wait  # Wait for all to complete
```

---

## 🎛️ **COORDINATION MODES EXPLAINED**

| **Mode** | **Best For** | **Agent Count** | **Use Case** |
|----------|--------------|-----------------|--------------|
| **centralized** | Simple tasks | 3-5 | Single coordinator, clear hierarchy |
| **distributed** | Independent tasks | 6-8 | Parallel research, analysis |
| **hierarchical** | Complex projects | 8-10 | Full-stack development |
| **mesh** | Collaborative work | 4-6 | Code review, consensus building |
| **hybrid** | Adaptive needs | 5-8 | Mixed workloads, optimization |

---

## 🎨 **SPARC MODES FOR PARALLEL WORK**

### **Core Orchestration (Multi-Agent)**
- `orchestrator` - Multi-agent task coordination
- `swarm-coordinator` - Advanced swarm management
- `batch-executor` - **⭐ KEY FOR PARALLEL TASKS**
- `workflow-manager` - Process automation

### **Development Modes (Parallel-Ready)**
- `coder` - Autonomous code generation
- `architect` - System design
- `reviewer` - Code review
- `tdd` - Test-driven development

### **Analysis & Research (Distributed)**
- `researcher` - Deep research with parallel WebSearch
- `analyzer` - Code and data analysis
- `optimizer` - Performance optimization

---

## 🔗 **MCP INTEGRATION WITH CLAUDE CODE**

### **Understanding MCP Transport Types**
- **STDIO**: Direct process communication (recommended for Claude Code)
- **HTTP**: Network-based (good for debugging, runs on port 3000)

### **Option A: STDIO Integration (Recommended)**

**1. Start MCP Server (STDIO Transport)**
```bash
./claude-flow mcp start --transport stdio
```

**2. Add to Claude Code**
```bash
claude mcp add claude-flow /Users/<USER>/Mister-Smith/Mister-Smith/claude-flow mcp start --transport stdio
```

**3. Verify Integration**
```bash
claude mcp list
claude mcp get claude-flow
```

### **Option B: HTTP Integration (For Debugging)**

**1. Start HTTP MCP Server**
```bash
./claude-flow mcp start --transport http --port 3000
```

**2. Test with curl**
```bash
curl -X POST http://localhost:3000/rpc -d '{"jsonrpc":"2.0","id":1,"method":"system/health"}'
```

### **Troubleshooting MCP Integration**

**Port Conflicts:**
```bash
# Check what's using port 3000
lsof -i :3000
# Kill conflicting processes
kill -9 $(lsof -t -i:3000)
```

**Configuration Issues:**
```bash
# Check MCP config
./claude-flow mcp status
# Restart MCP server
./claude-flow mcp stop
./claude-flow mcp start --transport stdio
```

**Claude Code Integration Test:**
In Claude Code conversation:
```
> Can you run: ./claude-flow status
> Can you run: ./claude-flow sparc modes
```

---

## 🛠️ **DAILY WORKFLOW EXAMPLES**

### **Morning Startup Routine**
```bash
cd /Users/<USER>/Mister-Smith/Mister-Smith
./claude-flow start --ui --port 3000
./claude-flow status
# Open http://localhost:3000 for monitoring
```

### **Feature Development (Multi-Agent)**
```bash
./claude-flow swarm "Implement user authentication with JWT, password hashing, email verification, and role-based access" \
  --strategy development \
  --mode hierarchical \
  --max-agents 6 \
  --parallel \
  --review \
  --monitor
```

### **Code Review & Optimization**
```bash
./claude-flow swarm "Review codebase for security vulnerabilities, performance issues, and code quality improvements" \
  --strategy analysis \
  --mode mesh \
  --max-agents 4 \
  --parallel \
  --review
```

### **Research & Planning**
```bash
./claude-flow swarm "Research best practices for microservices architecture, container orchestration, and CI/CD pipelines" \
  --strategy research \
  --mode distributed \
  --max-agents 5 \
  --parallel \
  --output sqlite
```

---

## 🚨 **TROUBLESHOOTING GUIDE**

### **System Not Fully Running**
```bash
./claude-flow stop
./claude-flow start --ui --port 3000
./claude-flow status
```

### **MCP Server Issues**
```bash
# Check what's using port 3000
lsof -i :3000
# Kill if needed
kill -9 $(lsof -t -i:3000)
# Restart MCP
./claude-flow mcp start --transport stdio
```

### **Memory Issues**
```bash
./claude-flow memory clear
./claude-flow memory status
```

### **Agent Cleanup**
```bash
./claude-flow agent list
./claude-flow agent terminate --all
```

### **Common Issues & Solutions**

**Issue: "batchtool not found"**
- **Solution**: BATCHTOOL is not a separate tool. Use:
  - `./claude-flow sparc run batch-executor` for parallel SPARC tasks
  - Shell parallel execution with `&` and `wait`
  - SWARM coordination for multi-agent work

**Issue: "swarm-benchmark not found"**
- **Solution**: Install if needed for benchmarking:
```bash
pip install swarm-benchmark
# or
cd benchmark && pip install -e .
```

**Issue: SPARC modes not working**
- **Solution**: Verify SPARC installation:
```bash
./claude-flow sparc modes
# If empty, reinitialize:
npx claude-flow@latest init --sparc --force
```

**Issue: Port conflicts**
- **Solution**: Use different ports:
```bash
./claude-flow start --ui --port 3001
./claude-flow mcp start --transport http --port 3002
```

---

## ⚡ **QUICK REFERENCE COMMANDS**

### **System Management**
```bash
./claude-flow start --ui --port 3000    # Start full system
./claude-flow status                     # Check system health
./claude-flow stop                       # Stop all services
./claude-flow --version                  # Check version
```

### **SPARC Commands**
```bash
./claude-flow sparc modes               # List all 17 modes
./claude-flow sparc run coder "task"    # Run specific mode
./claude-flow sparc run batch-executor "parallel tasks" --parallel
```

### **SWARM Commands**
```bash
./claude-flow swarm "objective" --strategy development --parallel --max-agents 8
./claude-flow swarm "objective" --mode distributed --monitor
./claude-flow swarm "objective" --dry-run  # Preview configuration
```

### **MCP Commands**
```bash
./claude-flow mcp start --transport stdio
./claude-flow mcp status
./claude-flow mcp tools
```

---

## 📊 **PERFORMANCE OPTIMIZATION**

### **For Maximum Parallel Performance**
```bash
# Increase agent limits in config
./claude-flow config set orchestrator.maxConcurrentAgents 15
./claude-flow config set terminal.poolSize 10

# Use hybrid coordination for adaptive scaling
./claude-flow swarm "complex task" --mode hybrid --max-agents 10 --parallel
```

### **Memory Management**
```bash
# Monitor memory usage
./claude-flow memory status
./claude-flow memory optimize

# Set retention policies
./claude-flow config set memory.retentionDays 7
```

---

## 🎯 **BEST PRACTICES**

1. **Always start with `./claude-flow start --ui`** for full functionality
2. **Use SWARM for complex multi-agent tasks** (your primary tool)
3. **Use batch-executor SPARC mode** for parallel SPARC operations
4. **Monitor via Web UI** at http://localhost:3000
5. **Use `--dry-run`** to preview complex swarm configurations
6. **Enable `--review`** for quality assurance in development tasks
7. **Use appropriate coordination modes** based on task complexity

---

## 🔄 **WHAT'S NEXT**

1. **Start the system**: `./claude-flow start --ui --port 3000`
2. **Test a simple swarm**: `./claude-flow swarm "create hello world API" --parallel`
3. **Set up MCP integration** with Claude Code
4. **Explore the Web UI** for monitoring and management
5. **Try complex multi-agent workflows** for your projects

**Your system is ready for powerful multi-agent parallel batch operations!** 🚀
