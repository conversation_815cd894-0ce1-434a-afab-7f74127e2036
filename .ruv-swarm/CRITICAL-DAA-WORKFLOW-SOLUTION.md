# CRITICAL DAA WORKFLOW SOLUTION - READ THIS FIRST

## 🚨 STOP: This Document Prevents Repeated Discovery Steps

### The Problem You're Solving
When implementing DAA workflows with ruv-swarm, sessions repeatedly encounter the same issues:
1. DAA agents don't appear in `agent_list` (by design - they're in a separate system)
2. Workflow execution fails with "agent[task.method] is not a function"
3. Confusion about two-tier architecture (Basic Swarm vs DAA System)

## ✅ THE SOLUTION: Complete Implementation Pattern

### Step 1: Initialize DAA Service (MANDATORY FIRST STEP)
```javascript
mcp__ruv-swarm__daa_init { 
  enableLearning: true, 
  enableCoordination: true,
  persistenceMode: "auto"
}
```

### Step 2: Create DAA Agents (NOT agent_spawn!)
```javascript
// ❌ WRONG - Creates basic swarm agents
mcp__ruv-swarm__agent_spawn { type: "coordinator" }

// ✅ CORRECT - Creates DAA agents
mcp__ruv-swarm__daa_agent_create { 
  id: "daa-coordinator-001",  // MUST prefix with "daa-"
  cognitivePattern: "systems",
  enableMemory: true,
  learningRate: 0.8
}
```

### Step 3: Workflow Format (CRITICAL)
```javascript
// ❌ WRONG - Causes "agent[task.method] is not a function"
{
  id: "step1",
  action: "analyze",
  type: "design"
}

// ✅ CORRECT - DAA-compatible format
{
  id: "step1",
  task: {
    method: "make_decision",  // MANDATORY
    args: [JSON.stringify({   // MUST be JSON string
      context: "analyze architecture",
      domain: "core",
      action: "execute"
    })]
  },
  description: "Analyze system architecture"
}
```

### Step 4: Handle Dependencies Properly
When workflow has dependencies, you CANNOT use parallel execution without checking:

```javascript
// For workflows with dependencies
if (workflow.dependencies && Object.keys(workflow.dependencies).length > 0) {
  // Execute sequentially to respect dependencies
  parallelExecution: false
} else {
  // Can execute in parallel
  parallelExecution: true
}
```

## 🏗️ Understanding Two-Tier Architecture

```
┌─────────────────────────────────────┐
│   Basic Swarm (96-97 agents)        │ <-- Visible in agent_list
│   - Coordination & Orchestration    │
│   - Task Distribution               │
│   - Cannot be removed               │
└──────────────┬──────────────────────┘
               │ Bridge Layer
               ▼
┌─────────────────────────────────────┐
│   DAA System (Your agents)          │ <-- NOT visible in agent_list
│   - Autonomous Learning             │
│   - Task Execution                  │
│   - Uses make_decision method       │
└─────────────────────────────────────┘
```

## 🔄 Workflow Transformation Function

Use this to convert any workflow to DAA format:

```javascript
function transformWorkflowToDAAFormat(workflow) {
  const daaSteps = [];
  
  if (workflow.stages) {
    workflow.stages.forEach(stage => {
      stage.steps.forEach(step => {
        daaSteps.push({
          id: step.id,
          task: {
            method: 'make_decision',
            args: [JSON.stringify({
              context: step.name,
              domain: step.domain || stage.id,
              agentType: step.agent_type,
              action: 'execute'
            })]
          },
          description: step.name
        });
      });
    });
  }
  
  return {
    id: workflow.id,
    name: workflow.name,
    steps: daaSteps,
    dependencies: workflow.dependencies || {},
    strategy: workflow.strategy || 'adaptive'
  };
}
```

## 📋 Complete Working Example

```javascript
// 1. Initialize DAA
mcp__ruv-swarm__daa_init { 
  enableLearning: true, 
  enableCoordination: true 
}

// 2. Create DAA agent
mcp__ruv-swarm__daa_agent_create { 
  id: "daa-test-001",
  cognitivePattern: "systems",
  enableMemory: true
}

// 3. Create workflow with correct format
mcp__ruv-swarm__daa_workflow_create {
  id: "test-workflow",
  name: "Test Workflow",
  steps: [{
    id: "step1",
    task: {
      method: "make_decision",
      args: [JSON.stringify({ context: "test task" })]
    }
  }],
  strategy: "adaptive"
}

// 4. Execute workflow
mcp__ruv-swarm__daa_workflow_execute {
  workflow_id: "test-workflow",
  agentIds: ["daa-test-001"],
  parallelExecution: true  // Only if no dependencies!
}
```

## 🚫 Common Mistakes to Avoid

1. **Using agent_spawn instead of daa_agent_create**
2. **Missing task.method structure in workflows**
3. **Not initializing DAA service first**
4. **Expecting DAA agents in agent_list**
5. **Ignoring workflow dependencies**
6. **Not prefixing DAA agent IDs with "daa-"**

## ✅ Verification Commands

```bash
# Check DAA status (NOT agent_list!)
mcp__ruv-swarm__daa_learning_status { detailed: true }

# Test agent functionality
mcp__ruv-swarm__daa_agent_adapt {
  agentId: "daa-test-001",
  feedback: "Test successful",
  performanceScore: 1.0
}

# Check workflow execution
mcp__ruv-swarm__task_status { detailed: true }
```

## 🎯 Quick Checklist for DAA Implementation

- [ ] Initialize DAA service first
- [ ] Use daa_agent_create (NOT agent_spawn)
- [ ] Prefix all DAA agent IDs with "daa-"
- [ ] Use task.method = "make_decision" format
- [ ] Convert workflows to DAA format
- [ ] Check dependencies before parallel execution
- [ ] Verify with DAA-specific commands (not agent_list)

---
**CRITICAL**: Save this document and READ IT FIRST in any future DAA implementation session to avoid repeating discovery steps.

Generated: 2025-07-05
Location: /Users/<USER>/Mister-Smith/MisterSmith/.ruv-swarm/CRITICAL-DAA-WORKFLOW-SOLUTION.md