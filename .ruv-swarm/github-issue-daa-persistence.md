# DAA Memory Persistence Not Functional - Agents/Workflows Lost Between Sessions

## Description

The DAA (Decentralized Autonomous Agents) system's memory persistence feature is not functioning as expected. While agents and workflows operate correctly within a session, all DAA state is lost when the session ends, requiring complete recreation in subsequent sessions.

## Current Behavior

1. `mcp__ruv-swarm__memory_usage` with `action: "store"` returns only memory size info, not confirmation
2. `mcp__ruv-swarm__memory_usage` with `action: "retrieve"` returns only memory size info, not stored data
3. DAA agents created with `daa_agent_create` vanish on session end
4. DAA workflows created with `daa_workflow_create` are lost between sessions
5. The `persistenceMode: "auto"` parameter in `daa_init` is accepted but has no effect

## Expected Behavior

- DAA agents and workflows should persist across sessions
- Memory storage API should actually store and retrieve data
- Cross-session continuity for autonomous learning agents

## Steps to Reproduce

```javascript
// Session 1
mcp__ruv-swarm__daa_init { 
  enableLearning: true, 
  enableCoordination: true,
  persistenceMode: "auto"
}

mcp__ruv-swarm__daa_agent_create {
  id: "test-agent-001",
  cognitivePattern: "systems",
  enableMemory: true
}

mcp__ruv-swarm__memory_usage {
  action: "store",
  key: "test-key",
  value: {"data": "test-value"}
}
// Returns: { total_mb: 48, wasm_mb: 48 }

// Session 2 (new session)
mcp__ruv-swarm__memory_usage {
  action: "retrieve",
  key: "test-key"
}
// Returns: { total_mb: 48, wasm_mb: 48 } // No actual data
```

## Environment

- ruv-swarm version: 1.0.11
- Platform: darwin
- MCP integration: stdio mode

## Impact

This prevents production use of DAA features for any application requiring state persistence, which is essential for autonomous learning agents.

## Proposed Solution

### Short-term Workaround

Implement file-based persistence wrapper:

```javascript
// daa-persistence.js
const fs = require('fs');
const path = require('path');

class DAAPersistence {
  constructor(storageDir = '.daa-state') {
    this.storageDir = storageDir;
    if (!fs.existsSync(storageDir)) {
      fs.mkdirSync(storageDir, { recursive: true });
    }
  }

  saveState(sessionId, state) {
    const filePath = path.join(this.storageDir, `${sessionId}.json`);
    fs.writeFileSync(filePath, JSON.stringify(state, null, 2));
  }

  loadState(sessionId) {
    const filePath = path.join(this.storageDir, `${sessionId}.json`);
    if (fs.existsSync(filePath)) {
      return JSON.parse(fs.readFileSync(filePath, 'utf8'));
    }
    return null;
  }

  saveAgentConfig(agents, workflows) {
    this.saveState('daa-config', { agents, workflows, timestamp: new Date().toISOString() });
  }

  restoreAgentConfig() {
    return this.loadState('daa-config');
  }
}
```

### Long-term Fix

The memory_usage function in the MCP server needs to implement actual storage operations:

```javascript
// Suggested implementation pattern
async function memory_usage({ action, key, value }) {
  switch (action) {
    case 'store':
      await storage.set(key, value);
      return { success: true, key, size: JSON.stringify(value).length };
    
    case 'retrieve':
      const data = await storage.get(key);
      return { success: true, key, value: data };
    
    case 'list':
      const keys = await storage.list(pattern);
      return { success: true, keys };
    
    default:
      return { total_mb: 48, wasm_mb: 48 }; // Current behavior for backward compatibility
  }
}
```

## Additional Context

- Neural model persistence works correctly via file export/import
- Basic memory notes persist properly
- Only DAA-specific state lacks persistence
- This affects all multi-session DAA workflows

## Related

This may be related to the broader state management architecture. The neural models use file-based persistence successfully, suggesting the infrastructure exists but isn't connected to the DAA memory layer.

---

**Labels to add**: bug, persistence, daa, memory