/**
 * Neural Training Manager for ruv-swarm integration
 */

const { spawn } = require('child_process');

class TrainingManager {
  constructor() {
    this.models = new Map();
    this.trainingHistory = [];
  }
  
  async trainAgent(agentId, trainingData = {}) {
    return new Promise((resolve) => {
      const args = [
        '--agentId', agentId,
        '--iterations', trainingData.iterations || 10
      ];
      
      spawn(`npx ruv-swarm mcp call neural_train ${args.join(' ')}`, { shell: true })
        .on('close', (code) => {
          const result = {
            agentId,
            trainedAt: Date.now(),
            success: code === 0
          };
          this.trainingHistory.push(result);
          resolve(result);
        });
    });
  }
  
  async adjustPattern(agentId, newPattern) {
    return new Promise((resolve) => {
      const args = [
        '--agent_id', agentId,
        '--action', 'change',
        '--pattern', newPattern
      ];
      
      spawn(`npx ruv-swarm mcp call daa_cognitive_pattern ${args.join(' ')}`, { shell: true })
        .on('close', resolve);
    });
  }
  
  getTrainingHistory(agentId) {
    return this.trainingHistory.filter(h => h.agentId === agentId);
  }
}

module.exports = TrainingManager;