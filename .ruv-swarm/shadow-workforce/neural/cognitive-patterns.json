{"patterns": {"systems": {"description": "Systems thinking for architecture and design", "weights": {"analysis": 0.8, "synthesis": 0.9, "abstraction": 0.85, "integration": 0.95}, "activation": "relu", "learningModifier": 1.2}, "analytical": {"description": "Deep analysis and data processing", "weights": {"analysis": 0.95, "synthesis": 0.7, "abstraction": 0.75, "integration": 0.8}, "activation": "tanh", "learningModifier": 1.0}, "critical": {"description": "Security and risk assessment", "weights": {"analysis": 0.9, "synthesis": 0.6, "abstraction": 0.7, "integration": 0.75}, "activation": "sigmoid", "learningModifier": 1.3}, "adaptive": {"description": "Flexible problem solving", "weights": {"analysis": 0.75, "synthesis": 0.85, "abstraction": 0.8, "integration": 0.9}, "activation": "relu", "learningModifier": 1.1}, "systematic": {"description": "Process and workflow optimization", "weights": {"analysis": 0.85, "synthesis": 0.75, "abstraction": 0.7, "integration": 0.85}, "activation": "relu", "learningModifier": 1.0}, "methodical": {"description": "Step-by-step execution", "weights": {"analysis": 0.8, "synthesis": 0.65, "abstraction": 0.6, "integration": 0.7}, "activation": "sigmoid", "learningModifier": 0.9}, "innovative": {"description": "Creative problem solving", "weights": {"analysis": 0.7, "synthesis": 0.95, "abstraction": 0.9, "integration": 0.85}, "activation": "tanh", "learningModifier": 1.4}, "holistic": {"description": "Big picture coordination", "weights": {"analysis": 0.75, "synthesis": 0.9, "abstraction": 0.95, "integration": 0.95}, "activation": "relu", "learningModifier": 1.2}}, "interactions": {"systems-analytical": 0.8, "critical-systematic": 0.85, "adaptive-innovative": 0.9, "holistic-systems": 0.95, "methodical-systematic": 0.88}}