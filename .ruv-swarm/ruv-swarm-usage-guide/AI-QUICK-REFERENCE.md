# ruv-swarm AI Quick Reference Index

This file is optimized for <PERSON> (<PERSON>) to quickly find answers. Search this file first before diving into detailed docs.

## 🔴 ERRORS → SOLUTIONS

### Error: "Invalid agent type 'architect'"
```javascript
// ❌ WRONG
mcp__ruv-swarm__agent_spawn { type: "architect" }

// ✅ CORRECT  
mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Chief Architect" }
```
**Valid types:** researcher, coder, analyst, optimizer, coordinator, tester, reviewer, documenter

### Error: "Cannot read property of undefined" 
```javascript
// ✅ Initialize DAA first!
mcp__ruv-swarm__daa_init { enableLearning: true, enableCoordination: true }
mcp__ruv-swarm__swarm_init { topology: "mesh" }
mcp__ruv-swarm__agent_spawn { type: "researcher" }
```
**Rule:** DAA → Swarm → Agents (in that order)

### Error: "MCP server not found"
```bash
# Check status
claude mcp list

# Reinstall
claude mcp add ruv-swarm npx ruv-swarm mcp start
```

### Error: "Sequential execution detected"
```javascript
// ❌ NEVER DO THIS
Message 1: swarm_init
Message 2: agent_spawn
Message 3: agent_spawn

// ✅ ALWAYS BATCH
[BatchTool]:
  mcp__ruv-swarm__swarm_init { topology: "mesh" }
  mcp__ruv-swarm__agent_spawn { type: "researcher" }
  mcp__ruv-swarm__agent_spawn { type: "coder" }
```

## 🎯 COMMON TASKS → CODE

### Task: Create shadow workforce (30 agents)
```javascript
[BatchTool]:
  // 1. Initialize DAA
  mcp__ruv-swarm__daa_init { enableLearning: true, enableCoordination: true }
  
  // 2. Initialize swarm
  mcp__ruv-swarm__swarm_init { topology: "hierarchical", maxAgents: 30, strategy: "specialized" }
  
  // 3. Spawn all agents at once
  mcp__ruv-swarm__agent_spawn { type: "coordinator", name: "Core Architecture Lead" }
  mcp__ruv-swarm__agent_spawn { type: "coder", name: "Async Specialist" }
  mcp__ruv-swarm__agent_spawn { type: "analyst", name: "Data Architect" }
  // ... continue for all 30 agents
  
  // 4. Configure hooks
  Write ".claude/settings.json" // with hook configuration. DO NOT OVERWRITE IF THERE!
```

### Task: Troubleshoot performance issues
```javascript
// Get full diagnostics
[BatchTool]:
  mcp__ruv-swarm__benchmark_run { type: "all", iterations: 5 }
  mcp__ruv-swarm__memory_usage { detail: "by-agent" }
  mcp__ruv-swarm__agent_metrics { metric: "performance" }
  mcp__ruv-swarm__daa_performance_metrics { category: "all" }
```

### Task: Train neural agents
```javascript
// Training happens 3 ways:
// 1. Automatic via hooks (post-edit)
// 2. Explicit training
mcp__ruv-swarm__neural_train { agentId: "agent-id", iterations: 10 }
// 3. Within workflow execution (demonstrate-learning step)
```

### Task: Check swarm status
```javascript
[BatchTool]:
  mcp__ruv-swarm__swarm_status { verbose: true }
  mcp__ruv-swarm__agent_list { filter: "all" }
  mcp__ruv-swarm__daa_learning_status { detailed: true }
```

## 🔑 KEY CONCEPTS

### Topology Types
- **mesh**: All agents connected (best for exploration)
- **hierarchical**: Tree structure (best for large teams)  
- **star**: Central coordinator (best for simple tasks)
- **ring**: Circular pattern (best for sequential processing)

### Agent Types (MEMORIZE THIS!)
researcher, coder, analyst, optimizer, coordinator, tester, reviewer, documenter

### Cognitive Patterns
- **convergent**: Focus on solutions
- **divergent**: Explore possibilities
- **lateral**: Creative approaches
- **systems**: Holistic thinking
- **critical**: Analytical evaluation
- **adaptive**: Learning and adjusting

### Parallel Execution Rules
1. NEVER send multiple messages
2. ALWAYS use BatchTool
3. Batch by operation type
4. Initialize in order: DAA → Swarm → Agents

## 🔗 CROSS-REFERENCES

- **"How to create agents?"** → See SHADOW-WORKFORCE-CREATION-PROCESS.md sections 6-7
- **"Parallel patterns?"** → See MULTI_AGENT_PARALLEL_PATTERNS.md
- **"Troubleshooting?"** → Start here, then RUV-SWARM-TROUBLESHOOTING-GUIDE.md
- **"Hook configuration?"** → See CLAUDE.md section on hooks
- **"Neural training?"** → See this file "Train neural agents" + SHADOW-WORKFORCE-CREATION-PROCESS.md section 10
- **"Visual guides?"** → Image-Examples/step-process-*.png

## 📊 QUICK DIAGNOSTICS

```bash
# One command to rule them all
npx ruv-swarm diagnose --verbose

# Quick MCP check
claude mcp list | grep ruv-swarm

# Test hook
npx ruv-swarm hook pre-task --description "test" --debug true
```

## 🚀 INSTANT SOLUTIONS

### "Make it work NOW"
```javascript
[BatchTool]:
  mcp__ruv-swarm__daa_init { enableLearning: true, enableCoordination: true }
  mcp__ruv-swarm__swarm_init { topology: "mesh", maxAgents: 5 }
  mcp__ruv-swarm__agent_spawn { type: "researcher" }
  mcp__ruv-swarm__agent_spawn { type: "coder" }
  mcp__ruv-swarm__agent_spawn { type: "coordinator" }
  mcp__ruv-swarm__task_orchestrate { task: "Hello World", strategy: "parallel" }
```

### "Fix everything"
```bash
# Nuclear option
claude mcp stop ruv-swarm
rm -rf ~/.ruv-swarm/cache
claude mcp remove ruv-swarm  
claude mcp add ruv-swarm npx ruv-swarm mcp start
```

## 📝 SEARCH KEYWORDS

Errors: invalid-type undefined-property mcp-not-found sequential-execution timeout memory-failed
Tasks: create-workforce troubleshoot-performance train-agents check-status
Concepts: topology agent-types cognitive-patterns parallel-execution
Tools: swarm_init agent_spawn daa_init task_orchestrate neural_train

## 🔍 SYSTEMATIC TROUBLESHOOTING

If issues persist after checking above:

```
1. Check internal docs:
   - AI-QUICK-REFERENCE.md (this file)
   - TROUBLESHOOTING-REFERENCE.md for detailed solutions
   
2. Search project:
   - grep "error" ruv-fann-usage-docs.xml
   
3. Query external resources:
   - mcp__context7__resolve-library-id { libraryName: "ruv-swarm" }
   - mcp__github__search_issues { q: "repo:ruvnet/ruv-FANN [error]" }
   
4. Quick diagnostic:
   npx ruv-swarm diagnose --verbose
```

---
REMEMBER: This file is your first stop. If answer not here, then check detailed docs.