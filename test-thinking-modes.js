// Test file to demonstrate thinking modes
// This code has several issues we can analyze

function calculateUserScore(user) {
    let score = 0;
    
    // Issue 1: No input validation
    score += user.posts * 10;
    score += user.comments * 5;
    
    // Issue 2: Magic numbers
    if (user.karma > 1000) {
        score = score * 1.5;
    }
    
    // Issue 3: No error handling
    const bonusPoints = JSON.parse(user.metadata).bonus;
    score += bonusPoints;
    
    // Issue 4: Inconsistent return type
    if (score > 10000) {
        return "Expert";
    }
    return score;
}

// Test data
const testUser = {
    posts: 50,
    comments: 100,
    karma: 2000,
    metadata: '{"bonus": 250}'
};

console.log(calculateUserScore(testUser));